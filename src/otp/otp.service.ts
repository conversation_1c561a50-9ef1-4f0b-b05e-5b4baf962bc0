import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Otp, OtpDocument } from './schemas/otp.schema';

@Injectable()
export class OtpService {
  constructor(
    @InjectModel(Otp.name) private otpModel: Model<OtpDocument>,
  ) {}

  /**
   * Generate a 6-digit OTP code
   */
  private generateOtpCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Normalize Iranian mobile number to consistent format
   */
  private normalizeMobileNumber(mobileNumber: string): string {
    // Remove spaces and convert to string
    let normalized = mobileNumber.replace(/\s/g, '');
    
    // Convert +98 format to 09 format
    if (normalized.startsWith('+98')) {
      normalized = '0' + normalized.substring(3);
    }
    
    return normalized;
  }

  /**
   * Create and save a new OTP for the given mobile number
   */
  async createOtp(mobileNumber: string): Promise<{ code: string; expiresAt: Date }> {
    const normalizedMobile = this.normalizeMobileNumber(mobileNumber);
    
    // Invalidate any existing unused OTPs for this mobile number
    await this.otpModel.updateMany(
      { 
        mobileNumber: normalizedMobile, 
        isUsed: false,
        expiresAt: { $gt: new Date() }
      },
      { isUsed: true }
    );

    // Generate new OTP
    const code = this.generateOtpCode();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

    const otp = new this.otpModel({
      mobileNumber: normalizedMobile,
      code,
      expiresAt,
    });

    await otp.save();

    return { code, expiresAt };
  }

  /**
   * Verify OTP code for the given mobile number
   */
  async verifyOtp(mobileNumber: string, code: string): Promise<boolean> {
    const normalizedMobile = this.normalizeMobileNumber(mobileNumber);
    
    const otp = await this.otpModel.findOne({
      mobileNumber: normalizedMobile,
      code,
      isUsed: false,
      expiresAt: { $gt: new Date() }
    });

    if (!otp) {
      // Increment attempts for security tracking
      await this.otpModel.updateMany(
        { 
          mobileNumber: normalizedMobile, 
          isUsed: false,
          expiresAt: { $gt: new Date() }
        },
        { $inc: { attempts: 1 } }
      );
      return false;
    }

    // Mark OTP as used
    otp.isUsed = true;
    await otp.save();

    return true;
  }

  /**
   * Clean up expired OTPs (can be called by a cron job)
   */
  async cleanupExpiredOtps(): Promise<void> {
    await this.otpModel.deleteMany({
      expiresAt: { $lt: new Date() }
    });
  }
}
