import { 
  Controller, 
  Post, 
  Body, 
  HttpCode, 
  HttpStatus,
  Get,
  Param,
  BadRequestException
} from '@nestjs/common';
import { UsersService } from './users.service';
import { SendOtpDto } from './dto/send-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  /**
   * Send OTP to user's mobile number
   * If user doesn't exist, register them first
   */
  @Post('send-otp')
  @HttpCode(HttpStatus.OK)
  async sendOtp(@Body() sendOtpDto: SendOtpDto) {
    try {
      const result = await this.usersService.sendOtp(sendOtpDto);
      
      // In production, don't return the actual code in the response
      // Instead, send it via SMS service
      return {
        success: true,
        message: result.message,
        data: {
          mobileNumber: sendOtpDto.mobileNumber,
          expiresAt: result.expiresAt,
          isNewUser: result.isNewUser,
          // Remove this line in production:
          code: result.code, // Only for development/testing
        }
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Verify OTP code
   */
  @Post('verify-otp')
  @HttpCode(HttpStatus.OK)
  async verifyOtp(@Body() verifyOtpDto: VerifyOtpDto) {
    try {
      const result = await this.usersService.verifyOtp(verifyOtpDto);
      
      return {
        success: result.isValid,
        message: result.message,
        data: result.isValid ? {
          user: {
            id: result.user.id,
            mobileNumber: result.user.mobileNumber,
            isActive: result.user.isActive,
            lastLoginAt: result.user.lastLoginAt,
            createdAt: (result.user as any).createdAt,
            updatedAt: (result.user as any).updatedAt,
          }
        } : null
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Get all users (for admin purposes)
   */
  @Get()
  async findAll() {
    const users = await this.usersService.findAll();
    return {
      success: true,
      data: users
    };
  }

  /**
   * Get user by ID
   */
  @Get(':id')
  async findOne(@Param('id') id: string) {
    try {
      const user = await this.usersService.findOne(id);
      return {
        success: true,
        data: user
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
