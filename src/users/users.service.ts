import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User, UserDocument } from './schemas/user.schema';
import { OtpService } from '../otp/otp.service';
import { SendOtpDto } from './dto/send-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private otpService: OtpService,
  ) {}

  /**
   * Normalize Iranian mobile number to consistent format
   */
  private normalizeMobileNumber(mobileNumber: string): string {
    // Remove spaces and convert to string
    let normalized = mobileNumber.replace(/\s/g, '');
    
    // Convert +98 format to 09 format
    if (normalized.startsWith('+98')) {
      normalized = '0' + normalized.substring(3);
    }
    
    return normalized;
  }

  /**
   * Find user by mobile number
   */
  async findByMobileNumber(mobileNumber: string): Promise<UserDocument | null> {
    const normalizedMobile = this.normalizeMobileNumber(mobileNumber);
    return this.userModel.findOne({ mobileNumber: normalizedMobile }).exec();
  }

  /**
   * Create a new user
   */
  async createUser(mobileNumber: string): Promise<UserDocument> {
    const normalizedMobile = this.normalizeMobileNumber(mobileNumber);
    const user = new this.userModel({ mobileNumber: normalizedMobile });
    return user.save();
  }

  /**
   * Send OTP to user's mobile number
   * If user doesn't exist, create them first
   */
  async sendOtp(sendOtpDto: SendOtpDto): Promise<{ 
    message: string; 
    code: string; 
    expiresAt: Date;
    isNewUser: boolean;
  }> {
    const { mobileNumber } = sendOtpDto;
    
    // Check if user exists
    let user = await this.findByMobileNumber(mobileNumber);
    let isNewUser = false;

    // If user doesn't exist, create them
    if (!user) {
      user = await this.createUser(mobileNumber);
      isNewUser = true;
    }

    // Generate and send OTP
    const { code, expiresAt } = await this.otpService.createOtp(mobileNumber);

    return {
      message: isNewUser 
        ? 'User registered successfully. OTP sent to your mobile number.' 
        : 'OTP sent to your mobile number.',
      code, // In production, this should be sent via SMS, not returned in response
      expiresAt,
      isNewUser,
    };
  }

  /**
   * Verify OTP and authenticate user
   */
  async verifyOtp(verifyOtpDto: VerifyOtpDto): Promise<{
    message: string;
    user: UserDocument;
    isValid: boolean;
  }> {
    const { mobileNumber, code } = verifyOtpDto;
    
    // Find user
    const user = await this.findByMobileNumber(mobileNumber);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Verify OTP
    const isValid = await this.otpService.verifyOtp(mobileNumber, code);
    
    if (isValid) {
      // Update last login time
      user.lastLoginAt = new Date();
      await user.save();
    }

    return {
      message: isValid ? 'OTP verified successfully' : 'Invalid or expired OTP',
      user,
      isValid,
    };
  }

  /**
   * Get all users (for admin purposes)
   */
  async findAll(): Promise<UserDocument[]> {
    return this.userModel.find().exec();
  }

  /**
   * Get user by ID
   */
  async findOne(id: string): Promise<UserDocument> {
    const user = await this.userModel.findById(id).exec();
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }
}
